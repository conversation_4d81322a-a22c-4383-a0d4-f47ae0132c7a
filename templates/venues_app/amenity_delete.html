{% extends 'base.html' %}
{% load widget_tweaks i18n %}

{% block title %}Delete Amenity - {{ venue.venue_name }} - CozyWish{% endblock %}

{% block extra_css %}
<!-- Preconnect for Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Amenity Delete */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    .venues-wrapper {
        background: var(--cw-gradient-hero);
        min-height: 100vh;
        padding: 2rem 0;
        font-family: var(--cw-font-primary);
    }

    .venues-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--cw-brand-accent);
    }

    .venues-header h2 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin: 0;
    }

    .venues-header p {
        color: var(--cw-neutral-600);
        margin: 0.5rem 0 0 0;
    }

    .delete-confirmation {
        background: white;
        border: 2px solid var(--cw-error, #dc2626);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: var(--cw-shadow-lg);
    }

    .amenity-preview {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .amenity-preview h5 {
        color: var(--cw-brand-primary);
        margin-bottom: 0.5rem;
    }

    .amenity-preview p {
        color: var(--cw-neutral-600);
        margin: 0;
    }

    /* Custom Buttons */
    .btn-cw-danger {
        background: var(--cw-error, #dc2626);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        color: white;
        transition: all 0.2s ease;
        box-shadow: var(--cw-shadow-sm);
    }
    .btn-cw-danger:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        color: white;
    }

    .btn-cw-secondary {
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: white;
        transition: all 0.2s ease;
    }
    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-1px);
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    @media (min-width: 576px) {
        .action-buttons {
            flex-direction: row;
        }
    }

    .warning-icon {
        color: var(--cw-error, #dc2626);
        font-size: 3rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="venues-wrapper">
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">

                <!-- Header -->
                <div class="venues-header d-flex align-items-center">
                    <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary me-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div>
                        <h2>Delete Amenity</h2>
                        <p>Confirm deletion of amenity for {{ venue.venue_name }}</p>
                    </div>
                </div>

                <!-- Delete Confirmation -->
                <div class="delete-confirmation text-center">
                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                    <h4 class="mb-3" style="color: var(--cw-brand-primary);">Are you sure you want to delete this amenity?</h4>
                    <p class="mb-4" style="color: var(--cw-neutral-600);">This action cannot be undone.</p>

                    <!-- Amenity Preview -->
                    <div class="amenity-preview text-start">
                        <h5>
                            {% if amenity.custom_name %}
                                {{ amenity.custom_name }}
                            {% else %}
                                {{ amenity.get_amenity_type_display }}
                            {% endif %}
                        </h5>
                        {% if amenity.description %}
                            <p>{{ amenity.description }}</p>
                        {% endif %}
                    </div>

                    <form method="post">
                        {% csrf_token %}

                        <!-- Action Buttons -->
                        <div class="action-buttons justify-content-center">
                            <button type="submit" class="btn btn-cw-danger">
                                <i class="fas fa-trash me-2"></i>Yes, Delete Amenity
                            </button>
                            <a href="{% url 'venues_app:manage_amenities' %}" class="btn btn-cw-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</div>
{% endblock %}
